require "rails_helper"

RSpec.describe Permission, type: :model do
  describe "records" do
    it "count" do
      expect(described_class.count).to eq(417)
    end

    it "I18n" do
      expect(described_class.find("9092").i18n).to eq("設定 > CSV 管理 > 定期受注 CSV 管理")
    end

    # inventory_location
    it "find_by_target" do
      expect(described_class.find_by(target: "在庫ロケーション管理").count).to eq(3)
    end
  end
end
