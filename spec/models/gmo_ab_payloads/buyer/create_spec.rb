require "rails_helper"

module GmoAbPayloads
  module Buyer
    RSpec.describe Create do
      before do
        Payment.aasm.state_machine.config.no_direct_assignment = false
        OrderState.init_records
        OrderSetting.init_records
        SubsOrderSetting.init_records
      end

      describe "methods" do
        let!(:sex) { create(:sex) }
        let!(:record) {
          described_class.new(
            order: create(:order),
            customer: create(:customer, sex: sex),
            billing_address_data: OpenStruct.new(
              prefecture_id: 1,
              name01: "name01",
              kana01: "kana01",
              zip01: "zip01",
              addr01: "addr01",
              company_name: "company name",
            ),
            payment_type: 2,
          )
        }

        let!(:record1) {
          described_class.new(
            order: create(:order),
            customer: create(:customer),
            billing_address_data: OpenStruct.new(
              prefecture_id: 1,
              name01: "name01",
              kana01: "kana01",
              zip01: "zip01",
              addr01: "addr01",
              company_name: "company name",
            ),
            payment_type: 2,
          )
        }

        it "with sex id" do
          expect { record.create_payload }.not_to raise_error
        end

        it "no sex id" do
          expect { record1.create_payload }.not_to raise_error
        end
      end
    end
  end
end
