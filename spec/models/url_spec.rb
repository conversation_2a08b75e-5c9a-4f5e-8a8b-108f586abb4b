# == Schema Information
#
# Table name: urls
#
#  id                                                            :bigint           not null, primary key
#  base_url(URL)                                                 :string(255)      not null
#  buy_multiple_variant(複数商品購入)                            :boolean          default(FALSE)
#  cost(コスト)                                                  :integer          default(0)
#  cv_counts(CV Counts)                                          :integer          default(0), not null
#  cv_upsell(サンクスオファー)                                   :boolean          default(FALSE)
#  default(デフォルト)                                           :boolean          default(FALSE)
#  deleted_at                                                    :datetime
#  description(説明)                                             :text(16777215)
#  lock_version                                                  :integer          not null
#  skip_confirm(Skip Confirm)                                    :boolean          default(FALSE), not null
#  skip_cv_confirm(Skip Cv Confirm)                              :boolean          default(FALSE), not null
#  state(ステータス)                                             :boolean          default(TRUE), not null
#  upsell(アップセル)                                            :boolean          default(FALSE)
#  created_at                                                    :datetime         not null
#  updated_at                                                    :datetime         not null
#  confirm_pc_template_id(確認ページ PC テンプレート)            :bigint
#  confirm_sp_template_id(確認ページ SP テンプレート)            :bigint
#  cv_confirm_pc_template_id(サンクス確認ページ PC テンプレート) :bigint
#  cv_confirm_sp_template_id(サンクス確認ページ SP テンプレート) :bigint
#  cv_upsell_pc_template_id(サンクスオファー PC テンプレート)    :bigint
#  cv_upsell_sp_template_id(サンクスオファー SP テンプレート)    :bigint
#  new_pc_template_id(LP PC テンプレート)                        :bigint
#  new_sp_template_id(LP SP テンプレート)                        :bigint
#  perform_pc_template_id(サンクスページ PC テンプレート)        :bigint
#  perform_sp_template_id(サンクスページ SP テンプレート)        :bigint
#  upsell_pc_template_id(アップセル PC テンプレート)             :bigint
#  upsell_sp_template_id(アップセル SP テンプレート)             :bigint
#  url_copy_from_url_id(Url Copy From Url Id)                    :bigint
#  url_group_id(Url Group Id)                                    :bigint
#
# Indexes
#
#  index_urls_on_base_url  (base_url) UNIQUE
#
require "rails_helper"

RSpec.describe Url do
  before do
    Payment.aasm.state_machine.config.no_direct_assignment = false
    OrderSetting.init_records
    SubsOrderSetting.init_records
  end

  describe ".valid_cv_upsell_with_order_and_products?" do
    subject { url.valid_cv_upsell_with_order_and_product?(order, product_id, 0) }

    let!(:url) { create(:url) }

    context "with nil params" do
      let!(:order) { nil }
      let!(:product_id) { nil }

      it do
        expect(subject).to be(false)
      end
    end

    context "when invalid valid_thank_offer?" do
      let!(:order) { "dummy" }
      let!(:product_id) { "dummy" }

      before do
        allow_any_instance_of(described_class).to receive(:valid_thank_offer?).and_return(false)
      end

      it do
        expect(subject).to be(false)
      end
    end

    context "with: valid valid_thank_offer?" do
      before do
        allow_any_instance_of(described_class).to receive(:valid_thank_offer?).and_return(true)
      end

      context "with: invalid order" do
        let!(:order) {
          OpenStruct.new(
            {
              url: "dummy",
            },
          )
        }
        let!(:product_id) { "dummy" }

        it do
          expect(subject).to be(false)
        end
      end

      context "with: valid order" do
        before do
          OrderState.init_records
        end

        let!(:order) { create(:order, url: url) }
        let!(:product_of_url) { create(:product, :with_variant_master) }
        let!(:product) { create(:product, :with_variant_master) }
        let!(:product_id) { "dummy" }

        context "with valid product purchased from url" do
          before do
            create(:products_url, url:, product: product_of_url)
            create(:order_item, sourceable: order, product: product_of_url, variant: product_of_url.variant_master)
          end

          context "when: found cv_upsell_product" do
            let!(:input_product) { create(:product) }
            let!(:product_id) { input_product.id }

            before do
              create(:product_next_offer, product: product_of_url, position: 0)
              create(:product_cv_upsell, product: product_of_url, cv_upsell_product: input_product, product_next_offer: product_of_url.product_next_offers.first)
            end

            it do
              expect(subject).to be(true)
            end
          end
        end
      end
    end
  end

  describe "order_settings_url" do
    context "after init has order_settings_url record" do
      let!(:url) { create(:url) }

      it do
        expect(url.order_settings_url.present?).to be(true)
      end
    end

    context "order_setting_url's fields value" do
      let!(:url) { create(:url) }

      context "default true as global_setting" do
        it do
          expect(url.order_settings_url_allow_schedule_delivery_date).to be(true)
          expect(url.order_settings_url_allow_nil_in_delivery_date).to be(true)
          expect(url.order_settings_url_allow_schedule_delivery_time).to be(true)
          expect(url.order_settings_url_show_scheduled_delivery_dates).to be(true)
        end
      end

      context "using '1' as value" do
        before do
          url.order_settings_url.update!(
            allow_schedule_delivery_date: "1",
            allow_nil_in_delivery_date: "1",
            allow_schedule_delivery_time: "1",
            show_scheduled_delivery_dates: "1",
          )
        end

        it do
          expect(url.order_settings_url_allow_schedule_delivery_date).to be(true)
          expect(url.order_settings_url_allow_nil_in_delivery_date).to be(true)
          expect(url.order_settings_url_allow_schedule_delivery_time).to be(true)
          expect(url.order_settings_url_show_scheduled_delivery_dates).to be(true)
        end
      end

      context "using '0' as value" do
        before do
          url.order_settings_url.update!(
            allow_schedule_delivery_date: "0",
            allow_nil_in_delivery_date: "0",
            allow_schedule_delivery_time: "0",
            show_scheduled_delivery_dates: "0",
          )
        end

        it do
          expect(url.order_settings_url_allow_schedule_delivery_date).to be(false)
          expect(url.order_settings_url_allow_nil_in_delivery_date).to be(false)
          expect(url.order_settings_url_allow_schedule_delivery_time).to be(false)
          expect(url.order_settings_url_show_scheduled_delivery_dates).to be(false)
        end
      end
    end
  end

  describe "subs_order_settings_url" do
    context "after init has order_settings_url record" do
      let!(:url) { create(:url) }

      it do
        expect(url.subs_order_settings_url.present?).to be(true)
      end
    end

    context "subs_order_setting_url's fields value" do
      let!(:url) { create(:url) }

      context "default true as global_setting" do
        it do
          expect(url.subs_order_settings_url_allow_schedule_delivery_date).to be(true)
          expect(url.subs_order_settings_url_allow_nil_in_delivery_date).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_time).to be(true)
          expect(url.subs_order_settings_url_show_scheduled_delivery_dates).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_date).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_term).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_day_of_week).to be(true)
        end
      end

      context "using '1' as value" do
        before do
          url.subs_order_settings_url.update!(
            allow_schedule_delivery_date: "1",
            allow_nil_in_delivery_date: "1",
            allow_schedule_delivery_time: "1",
            show_scheduled_delivery_dates: "1",
            allow_schedule_delivery_cycle_by_date: "1",
            allow_schedule_delivery_cycle_by_term: "1",
            allow_schedule_delivery_cycle_by_day_of_week: "1",
          )
        end

        it do
          expect(url.subs_order_settings_url_allow_schedule_delivery_date).to be(true)
          expect(url.subs_order_settings_url_allow_nil_in_delivery_date).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_time).to be(true)
          expect(url.subs_order_settings_url_show_scheduled_delivery_dates).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_date).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_term).to be(true)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_day_of_week).to be(true)
        end
      end

      context "using '0' as value" do
        before do
          url.subs_order_settings_url.update!(
            allow_schedule_delivery_date: "0",
            allow_nil_in_delivery_date: "0",
            allow_schedule_delivery_time: "0",
            show_scheduled_delivery_dates: "0",
            allow_schedule_delivery_cycle_by_date: "0",
            allow_schedule_delivery_cycle_by_term: "0",
            allow_schedule_delivery_cycle_by_day_of_week: "0",
          )
        end

        it do
          expect(url.subs_order_settings_url_allow_schedule_delivery_date).to be(false)
          expect(url.subs_order_settings_url_allow_nil_in_delivery_date).to be(false)
          expect(url.subs_order_settings_url_allow_schedule_delivery_time).to be(false)
          expect(url.subs_order_settings_url_show_scheduled_delivery_dates).to be(false)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_date).to be(false)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_term).to be(false)
          expect(url.subs_order_settings_url_allow_schedule_delivery_cycle_by_day_of_week).to be(false)
        end
      end
    end
  end
end
