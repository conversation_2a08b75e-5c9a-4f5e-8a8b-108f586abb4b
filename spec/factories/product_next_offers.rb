# == Schema Information
#
# Table name: product_next_offers
#
#  id                       :bigint           not null, primary key
#  default                  :boolean          default(FALSE), not null
#  position                 :integer          default(0), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  cv_upsell_pc_template_id :bigint
#  cv_upsell_sp_template_id :bigint
#  product_id               :bigint           not null
#
FactoryBot.define do
  factory :product_next_offer do
    association :product
    default { false }
    position { 0 }
    cv_upsell_pc_template { create(:template) }
    cv_upsell_sp_template { create(:template) }
  end
end
