require "rails_helper"

module Mutations
  module Admins
    module Customers
      module Softbank
        module Cards
          RSpec.describe Modify, type: :request do
            include_context "admins_context"

            describe "resolve" do
              let!(:admin) { create(:admin) }
              let!(:customer) { create(:customer) }

              context "when user doesnt login" do
                it do
                  request(customer.id)

                  expect(response).to have_http_status(:unauthorized)
                end
              end

              context "when user login" do
                before do
                  super_admin_logged_in
                end

                let!(:card) {
                  [
                    {
                      cardbrand_code: "V",
                      cc_expiration: "202312",
                      cc_number: "1234",
                    },
                  ]
                }
                let!(:success) { double("success", { success?: true, card: card }) }
                let!(:failed) { double("failed", { success?: false,  card: [], message: "error message" }) }

                context "when customer exists" do
                  context "when customer had card" do
                    before do
                      allow(::Payments::Softbank::Cards::ListCardService).to receive(:call).and_return(success)
                    end

                    context "when success" do
                      it do
                        allow(::Payments::Softbank::Cards::UpdateCardService).to receive(:call).and_return(success)

                        request(customer.id)
                        expect(response).to have_http_status(:ok)
                        json_data = JSON.parse(response.body).with_indifferent_access

                        expect(json_data.dig(:data, :adminsCustomersSoftbankCardModify, :message)).to eq(I18n.t("actions.customers.softbank.cards.modify.success"))
                      end
                    end

                    context "when failed" do
                      it do
                        allow(::Payments::Softbank::Cards::UpdateCardService).to receive(:call).and_return(failed)

                        request(customer.id)

                        expect(response).to have_http_status(:ok)
                        json_data = JSON.parse(response.body).with_indifferent_access

                        expect(json_data.dig(:data, :adminsCustomersSoftbankCardModify)).to be_nil
                        expect(json_data.dig(:errors).present?).to be(true)
                      end
                    end
                  end

                  context "when customer dont have card" do
                    before do
                      allow(::Payments::Softbank::Cards::ListCardService).to receive(:call).and_return(failed)
                    end

                    context "when success" do
                      it do
                        allow(::Payments::Softbank::Cards::CreateCardService).to receive(:call).and_return(success)

                        request(customer.id)

                        expect(response).to have_http_status(:ok)
                        json_data = JSON.parse(response.body).with_indifferent_access

                        expect(json_data.dig(:data, :adminsCustomersSoftbankCardModify, :message)).to eq(I18n.t("actions.customers.softbank.cards.modify.success"))
                      end
                    end

                    context "when failed" do
                      it do
                        allow(::Payments::Softbank::Cards::CreateCardService).to receive(:call).and_return(failed)

                        request(customer.id)

                        expect(response).to have_http_status(:ok)
                        json_data = JSON.parse(response.body).with_indifferent_access

                        expect(json_data.dig(:data, :adminsCustomersSoftbankCardModify)).to be_nil
                        expect(json_data.dig(:errors).present?).to be(true)
                      end
                    end
                  end
                end

                context "when customer non exists"
                it do
                  allow(::Payments::Softbank::Cards::ListCardService).to receive(:call).and_return(failed)

                  request(100)

                  expect(response).to have_http_status(:not_found)
                end
              end
            end

            def request(id)
              post "/graphql", headers: { 'Content-Type': "application/json" },
                               params: {
                                 query: query,
                                 variables: variables(id),
                               }.to_json
            end

            def variables(id)
              {
                customerId: id,
                cardToken: "cardToken",
                cardTokenKey: "cardTokenKey",
              }
            end

            def query
              <<~GQL
                  mutation($customerId: ID!, $cardToken: String!, $cardTokenKey: String!) {
                    adminsCustomersSoftbankCardModify(customerId: $customerId, cardToken: $cardToken, cardTokenKey: $cardTokenKey) {
                        message
                    }
                }
              GQL
            end
          end
        end
      end
    end
  end
end
