require "rails_helper"

module Mutations
  module Admins
    module Customers
      module Softbank
        module Cards
          RSpec.describe Destroy, type: :request do
            include_context "admins_context"

            describe "resolve" do
              let!(:admin) { create(:admin) }
              let!(:customer) { create(:customer) }

              context "when user doesnt login" do
                it do
                  request(customer.id)

                  expect(response).to have_http_status(:unauthorized)
                end
              end

              context "when user login" do
                before do
                  super_admin_logged_in
                end

                let!(:card) {
                  [
                    {
                      cardbrand_code: "V",
                      cc_expiration: "202312",
                      cc_number: "1234",
                    },
                  ]
                }
                let!(:success) { double("success", { success?: true, card: card }) }
                let!(:failed) { double("failed", { success?: false,  card: [], message: "error message" }) }

                context "when customer exists" do
                  context "when success" do
                    let!(:success) {
                      double("success", {
                               success?: true,
                             })
                    }

                    it do
                      allow(::Payments::Softbank::Cards::DeleteCardService).to receive(:call).and_return(success)

                      request(customer.id)

                      expect(response).to have_http_status(:ok)
                      json_data = JSON.parse(response.body).with_indifferent_access

                      expect(json_data.dig(:data, :adminsCustomersSoftbankCardDestroy, :message)).to eq(I18n.t("actions.customers.softbank.cards.destroy.success"))
                    end
                  end

                  context "when failed" do
                    let!(:failed) {
                      double("failed", {
                               success?: false,
                               message: "dummy error message",
                             })
                    }

                    it do
                      allow(::Payments::Softbank::Cards::DeleteCardService).to receive(:call).and_return(failed)

                      request(customer.id)

                      expect(response).to have_http_status(:ok)
                      json_data = JSON.parse(response.body).with_indifferent_access

                      expect(json_data.dig(:data, :adminsCustomersSoftbankCardDestroy)).to be_nil
                      expect(json_data.dig(:errors).present?).to be(true)
                      expect(json_data.dig(:errors).first.dig(:message)).to eq("dummy error message")
                    end
                  end
                end

                context "when customer non exists"
                it do
                  allow(::Payments::Softbank::Cards::ListCardService).to receive(:call).and_return(failed)

                  request(100)

                  expect(response).to have_http_status(:not_found)
                end
              end
            end

            def request(id)
              post "/graphql", headers: { 'Content-Type': "application/json" },
                               params: {
                                 query: query,
                                 variables: variables(id),
                               }.to_json
            end

            def variables(id)
              {
                customerId: id,
              }
            end

            def query
              <<~GQL
                  mutation($customerId: ID!) {
                    adminsCustomersSoftbankCardDestroy(customerId: $customerId) {
                        message
                    }
                }
              GQL
            end
          end
        end
      end
    end
  end
end
