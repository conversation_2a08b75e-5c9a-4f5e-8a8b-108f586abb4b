require "rails_helper"

module Mutations
  module Admins
    module Customers
      module Payjp
        module Cards
          RSpec.describe Create, type: :request do
            include_context "admins_context"

            describe "resolve" do
              let!(:admin) { create(:admin) }

              context "when user doesnt login" do
                it do
                  request(100)

                  expect(response).to have_http_status(:unauthorized)
                end
              end

              context "when user login" do
                before do
                  super_admin_logged_in
                end

                context "when customer exists" do
                  let!(:customer) { create(:customer) }

                  context "when success" do
                    let!(:success) {
                      double("success", {
                               success?: true,
                             })
                    }

                    it do
                      allow(::Payments::Payjp::Cards::CreateService).to receive(:call).and_return(success)

                      request(customer.id)

                      expect(response).to have_http_status(:ok)
                      json_data = JSON.parse(response.body).with_indifferent_access

                      expect(json_data.dig(:data, :adminsCustomersPayjpCardCreate, :message)).to eq(I18n.t("actions.customers.payjp.cards.create.success"))
                    end
                  end

                  context "when failed" do
                    let!(:failed) {
                      double("failed", {
                               success?: false,
                               message: "dummy error message",
                             })
                    }

                    it do
                      allow(::Payments::Payjp::Cards::CreateService).to receive(:call).and_return(failed)

                      request(customer.id)

                      expect(response).to have_http_status(:ok)
                      json_data = JSON.parse(response.body).with_indifferent_access

                      expect(json_data.dig(:data, :adminsCustomersPayjpCardCreate)).to be_nil
                      expect(json_data.dig(:errors).present?).to be(true)
                      expect(json_data.dig(:errors).first.dig(:message)).to eq("dummy error message")
                    end
                  end
                end

                context "when customer doesnt exist" do
                  it do
                    request(1000)

                    expect(response).to have_http_status(:not_found)
                  end
                end
              end
            end

            def request(id)
              post "/graphql", headers: { 'Content-Type': "application/json" },
                               params: {
                                 query: query,
                                 variables: variables(id),
                               }.to_json
            end

            def variables(id)
              {
                customerId: id,
                cardToken: "token",
              }
            end

            def query
              <<~GQL
                mutation($customerId: ID!, $cardToken: String!) {
                  adminsCustomersPayjpCardCreate(customerId: $customerId, cardToken: $cardToken) {
                    message
                  }
                }
              GQL
            end
          end
        end
      end
    end
  end
end
