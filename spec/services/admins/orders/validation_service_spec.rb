require "rails_helper"

module Ad<PERSON>
  module Orders
    RSpec.describe ValidationService, type: :service do
      before do
        Sex.init_records
      end

      subject { described_class.call(**attributes) }

      describe "call" do
        include_context "admins_order_input_context"

        let!(:init_customer) { ::Customer.new(email: "<EMAIL>") }
        let!(:attributes) {
          {
            customer: init_customer,
            billing_address_data: billing_address,
            shipping_address_data: {
              same_with_billing_address: true,
            },
            variant_data: variant_data,
            customer_data: customer_data,
            payment_data: {
              payment_method_code: "payjp",
              card_token: "dummy",
            },
            specify_delivery_date: {
              scheduled_delivery_time: nil,
              scheduled_to_be_delivered_at: nil,
            },
            shipping_carrier_id: shipping_carrier.id,
            subs_order_data: {},
          }
        }

        context "when success" do
          it do
            binding.pry
            allow(::Shops::Carts::SpecifyDeliveryDateForm).to receive(:new).and_return(success)

            expect(subject.success?).to be(true)
            expect(subject.error_messages).to be_empty
          end
        end

        context "when failed" do
          before do
            attributes[:variant_data] = []
          end

          it do
            expect(subject.fail).to be(true)
            expect(subject.error_messages.present?).to be(true)
            expect(subject.error_messages[:variant_data]).to eq("商品#{I18n.t("errors.messages.required")}")
          end
        end
      end
    end
  end
end
